# AI-Assisted Web Project Documentation

## General Description

This project is a web-based application developed with the assistance of an AI programming assistant. The objective is to create a complete web solution that handles a set of use cases in a structured, maintainable, and user-friendly manner.

The project focuses on managing and visualizing data from a relational database. It supports interactions such as data entry, consultation, modification, and deletion, all while maintaining data integrity. The AI assistant is used to assist in the generation of code, structure, and documentation.

All development, documentation, and UI content are in French. The technologies used are fundamental web technologies, including HTML, CSS, JavaScript, PHP, and MySQL.

---

## Relational Model

The database schema consists of the following main tables, designed to ensure referential integrity and normalization:

- **UTILISATEUR** (ID_UTILISATEUR, NOM, PRENOM, LOGIN, MOT_PASSE, TYPE)
- **CLASSE** (ID_CLASSE, NOM_CLASSE)
- **ETUDIANT** (ID_ETUDIANT, NOM, PRENOM, ID_CLASSE)
- **ENSEIGNANT** (ID_ENSEIGNANT, NOM, PRENOM)
- **MODULE** (ID_MODULE, NOM_MODULE, ID_<PERSON>NSEIGNANT)
- **NOTE** (ID_NOTE, ID_ETUDIANT, ID_MODULE, VALEUR)

Each table is linked using foreign keys to maintain consistency and reflect logical associations between entities. For example:

- A student (**ETUDIANT**) belongs to a class (**CLASSE**).
- A module (**MODULE**) is taught by an instructor (**ENSEIGNANT**).
- A note (**NOTE**) links a student and a module.

---

## Classes

The application uses JavaScript classes (in the frontend `model` folder) to represent domain entities. These include:

- `Utilisateur`: Represents a user of the system, including their role (student, teacher, admin).
- `Classe`: Represents a class entity to which students belong.
- `Etudiant`: Contains student-specific attributes and behaviors.
- `Enseignant`: Represents teachers and their related actions.
- `Module`: Represents a teaching module and its association with an instructor.
- `Note`: Represents grades assigned to students for specific modules.

Each class encapsulates relevant properties and includes methods for data validation and formatting as needed.

---

## Architecture and Folder Structure

The project follows the **MVC (Model-View-Controller)** architecture, organized into two main directories:

### `/frontend`
- **`/model/`**: Contains JavaScript class definitions for domain objects (e.g., `Etudiant.js`, `Module.js`, etc.).
- **`/view/`**: Includes subfolders for each use case scenario. Each subfolder contains HTML, CSS, and JS files responsible for the visual interface for that specific use case.
- **`/controller/`**: Contains JavaScript files that implement the business logic and call the appropriate methods from the classes in `/model`.

### `/backend`
- **`database.sql`**: SQL script to initialize and populate the MySQL database with the relational schema.
- **`/server/`**: Contains PHP scripts that act as the API layer, handling requests from the frontend, interacting with the database, and returning appropriate responses.

---

## Use Case Descriptions (with Scenarios)

Each use case is represented by a specific user interaction scenario, implemented with corresponding frontend and backend logic:

### Use Case: Add Student
**Scenario**: An admin fills out a form to add a new student to a specific class. Upon submission, the data is sent to a PHP backend script, which inserts the student into the database. Confirmation is then displayed to the user.

### Use Case: Assign Grade
**Scenario**: A teacher selects a student and a module, enters the grade, and submits the form. The backend verifies the associations and stores the note in the database.

### Use Case: Display Class Grades
**Scenario**: A user selects a class from a dropdown. The frontend fetches and displays all student grades in that class using a tabular format with filters.

### Use Case: Modify Student Info
**Scenario**: Admin searches for a student, edits their information, and updates it via a form. The PHP backend updates the record in the database accordingly.

---

## Architecture and Folder Structure

*(To be completed)*

---

## Additional Commands

- The official language for all code, documentation, and UI is **French**.
- The project uses only **basic technologies**: HTML, CSS, JavaScript, PHP, and MySQL.
- **Data consistency** is a priority: all interactions with the backend must respect the integrity constraints defined in the relational model.
- The application will use a **visually appealing UI**, designed for simplicity and clarity, to enhance user experience.

---
